import axios, { AxiosError, AxiosInstance, AxiosResponse, InternalAxiosRequestConfig } from "axios";
import Cookies from "js-cookie";

export const API_BASE_URL: string = import.meta.env.VITE_API_BASE_URL;

export const apiInstance: AxiosInstance = axios.create({
	baseURL: "/api",
	withCredentials: true,
	// timeout: 3000,
});

interface ITokenProvider {
	getToken(): string | undefined;
}

class TokenProvider implements ITokenProvider {
	getToken(): string | undefined {
		return Cookies.get("access_token");
	}
}

class ApiInterceptorManager {
	constructor(
		private axiosInstance: AxiosInstance,
		private tokenProvider: ITokenProvider
	) {}

	addRequestInterceptors(): void {
		this.axiosInstance.interceptors.request.use(
			(config: InternalAxiosRequestConfig): InternalAxiosRequestConfig => {
				const token: string | undefined = this.tokenProvider.getToken();
				if (token) {
					config.headers = config.headers || {};
					config.headers.Authorization = `Bearer ${token}`;
				}
				return config;
			},
			(error: AxiosError): Promise<AxiosError> => {
				return Promise.reject(error);
			}
		);
	}

	addResponseInterceptors(): void {
		this.axiosInstance.interceptors.response.use(
			(response: AxiosResponse): AxiosResponse => {
				const contentType = response.headers["content-type"];

				if (contentType && !contentType.includes("application/json") && !contentType.includes("application/pdf")) {
					throw new Error("Formato de resposta inesperado: esperado JSON ou PDF, mas recebido outro tipo.");
				}
				return response;
			},
			(error: AxiosError): Promise<AxiosError> => {
				if (error.response) {
					console.error("Response Error:", error.response);
				}
				return Promise.reject(error);
			}
		);
	}
}

const tokenProvider = new TokenProvider();
const interceptorManager = new ApiInterceptorManager(apiInstance, tokenProvider);

interceptorManager.addRequestInterceptors();
interceptorManager.addResponseInterceptors();

export default apiInstance;
