import { PersonClassificationEnum } from "@/modules/person/enums/person-classification.enum";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { useFindBillById } from "../../hooks/bills/find-bill-by-id.hook";
import { useUpdateBill } from "../../hooks/bills/update.hook";
import { convertToIsoDate } from "../../utils/date-utils";
import { CreateBillModal } from "./create-bill-modal";

const editBillSchema = z
	.object({
		type: z.number().optional(),
		description: z.string().optional(),
		value: z.number().positive("Valor deve ser maior que zero"),
		dueDate: z.string().optional(),
		paymentDate: z.string().optional(),
		personId: z.number().int().optional(),
		accountId: z.number().int().optional(),
	})
	.refine(
		data => {
			if (data.paymentDate && !data.accountId) {
				return false;
			}
			return true;
		},
		{
			message: "Conta é obrigatória quando há data de pagamento",
			path: ["accountId"],
		}
	);

type EditBillForm = z.infer<typeof editBillSchema>;

interface EditBillModalProps {
	isOpen: boolean;
	onClose: () => void;
	billId: number;
}

export const EditBillModal = ({ isOpen, onClose, billId }: EditBillModalProps) => {
	const updateBill = useUpdateBill();
	const { data: billData, isLoading: isLoadingBill } = useFindBillById({
		billId,
		enabled: isOpen,
	});

	const bill = billData && billData.success ? billData.data : undefined;

	const form = useForm<EditBillForm>({
		resolver: zodResolver(editBillSchema),
		defaultValues: {
			type: undefined,
			description: "",
			value: 0,
			dueDate: undefined,
			paymentDate: undefined,
			personId: undefined,
			accountId: undefined,
		},
	});

	useEffect(() => {
		if (bill) {
			const billType = bill.type === "A Pagar" ? PersonClassificationEnum.SUPPLIER : PersonClassificationEnum.CUSTOMER;
			form.reset({
				type: billType,
				description: bill.description,
				value: Number(bill.value.replace("R$", "").replace(".", "").replace(",", ".").trim()),
				dueDate: bill.dueDate
					? (() => {
							const [day, month, year] = bill.dueDate.split("/");
							return new Date(Number(year), Number(month) - 1, Number(day)).toISOString();
						})()
					: undefined,
				paymentDate: bill.paymentDate
					? (() => {
							const [day, month, year] = bill.paymentDate.split("/");
							return new Date(Number(year), Number(month) - 1, Number(day)).toISOString();
						})()
					: undefined,
				personId: bill.person?.id,
				accountId: bill.account?.id,
			});
		}
	}, [bill, form]);

	const onSubmit = async (data: EditBillForm) => {
		if (!bill) return;

		try {
			await updateBill.mutateAsync({
				id: billId,
				description: data.description,
				value: data.value,
				dueDate: convertToIsoDate(data.dueDate),
				paymentDate: convertToIsoDate(data.paymentDate),
				personId: data.personId,
				accountId: data.accountId,
			});
			onClose();
		} catch (error) {
			console.error("Erro ao atualizar conta:", error);
		}
	};

	return (
		<CreateBillModal
			isOpen={isOpen}
			isEdit
			onClose={onClose}
			title="Editar Conta"
			onSubmit={onSubmit}
			form={form}
			isLoading={updateBill.isPending || isLoadingBill}
		/>
	);
};
