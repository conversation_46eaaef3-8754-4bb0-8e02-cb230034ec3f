import { queryOptions, useQuery } from "@tanstack/react-query";
import { useAtom, useSet<PERSON>tom } from "jotai";
import Cookies from "js-cookie";
import { useEffect } from "react";
import { profileRequest } from "../api/profile.request";
import { authStateAtom } from "../states/auth.state";
import { userProfileAtom } from "../states/user.state";

export const profileQueryOptions = () =>
	queryOptions({
		queryKey: ["user-profile"],
		queryFn: async () => {
			const response = await profileRequest();
			return response;
		},
		enabled: true,
		retry: false,
	});

export const useUserProfile = () => {
	const [profile, setProfile] = useAtom(userProfileAtom);
	const setAuth = useSetAtom(authStateAtom);

	const { isLoading, error, data, refetch } = useQuery({
		...profileQueryOptions(),
	});

	useEffect(() => {
		if (data?.success) {
			setProfile(data.data);
			setAuth({ isAuthenticated: true });
		} else if (!data?.success && !isLoading) {
			setProfile(null);
			setAuth({ isAuthenticated: false });
			Cookies.remove("access_token");
			Cookies.remove("refresh_token");
		}
	}, [data, setProfile, setAuth, error, isLoading]);

	return {
		profile,
		isLoading,
		error,
		refetch,
	};
};
