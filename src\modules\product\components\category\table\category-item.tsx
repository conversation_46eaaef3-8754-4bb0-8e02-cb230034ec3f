import { But<PERSON> } from "@/shared/components/ui/button";
import { TableCell, TableRow } from "@/shared/components/ui/table";
import { Trash2 } from "lucide-react";
import React from "react";
import { EditCategoryModal } from "./edit-item";

interface CategoryData {
	id: number;
	name: string;
	totalProducts: number;
	totalSales: number;
	status: "Ativo" | "Inativo";
	icon: React.ElementType;
}

interface CategoryRowProps {
	category: CategoryData;
}

export function CategoryRow({ category }: CategoryRowProps) {
	const handleSave = (updatedCategory: CategoryData) => {
		console.log("Categoria atualizada:", updatedCategory);
	};

	const handleDelete = (id: number) => {
		console.log("Excluir categoria com id:", id);
	};

	return (
		<TableRow className="hover:bg-mainColor/5 transition-colors duration-200">
			<TableCell className="py-3 px-2">
				<div className="flex items-center gap-2">
					<category.icon className="h-6 w-6 text-mainColor" />
					<span className="font-medium text-gray-700">{category.name}</span>
				</div>
			</TableCell>

			<TableCell className="py-3 px-2 text-center">
				<span className="inline-flex items-center px-2 py-1 text-xs rounded-xl bg-mainColor/10 text-mainColor">{category.totalProducts}</span>
			</TableCell>

			<TableCell className="py-3 px-2 text-center">
				{category.totalSales.toLocaleString("pt-BR", {
					style: "currency",
					currency: "BRL",
				})}
			</TableCell>

			<TableCell className="py-3 px-2 text-center">
				{category.status === "Ativo" ? (
					<span className="inline-flex items-center px-2 py-1 text-xs rounded-xl bg-green-100 text-green-700">Ativo</span>
				) : (
					<span className="inline-flex items-center px-2 py-1 text-xs rounded-xl bg-red-100 text-red-600">Inativo</span>
				)}
			</TableCell>

			<TableCell className="py-3 px-2 text-right">
				<div className="flex items-center justify-end gap-3">
					<EditCategoryModal category={category} onSave={handleSave} onDelete={handleDelete} />
					<Button
						variant="ghost"
						className="p-0 hover:bg-transparent text-red-400 hover:text-red-500"
						onClick={() => alert(`Excluir: ${category.name}`)}
					>
						<Trash2 size={20} />
					</Button>
				</div>
			</TableCell>
		</TableRow>
	);
}
