import { PersonTypeEnum } from "@/modules/person/enums/person-classification.enum";
import { z } from "zod";

export const createBillSchema = z.object({
	type: z.nativeEnum(PersonTypeEnum, {
		required_error: "<PERSON><PERSON>cione o tipo da conta",
		invalid_type_error: "Tipo inválido",
	}),
	description: z.string().optional(),
	value: z.number().positive("Valor deve ser maior que zero"),
	dueDate: z.string().optional(),
	paymentDate: z.string().optional(),
	personId: z.number().int().optional(),
	accountId: z.number().int().optional(),
});

export type CreateBillForm = z.infer<typeof createBillSchema>;
