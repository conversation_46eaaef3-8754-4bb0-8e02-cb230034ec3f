import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import { Provider as <PERSON><PERSON><PERSON>rov<PERSON> } from "jotai";
import { queryClient } from "./query/create";
import { QueryProvider } from "./query/provider";

export const GlobalProvider = ({ children }: { children: React.ReactNode }) => {
	return (
		<JotaiProvider>
			<QueryProvider client={queryClient()} Devtools={ReactQueryDevtools}>
				{children}
			</QueryProvider>
		</JotaiProvider>
	);
};
