import { createRequest } from "@/shared/lib/create-request.lib";
import { IGlobalMessageReturn } from "@/shared/types/requests/global-message-return.type";
import { ApiResponse } from "@/shared/types/requests/requests.type";
import { ICreatePersonDTO } from "../../dtos/create-person.dto";
import { PERSON_ENDPOINTS } from "../endpoints";

export const createPersonRequest = async (data: ICreatePersonDTO): Promise<ApiResponse<IGlobalMessageReturn & { data: number }>> => {
	console.log("createPersonRequest", data);
	return createRequest({
		path: PERSON_ENDPOINTS.CREATE,
		method: "POST",
		body: data,
	});
};
