import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/shared/components/ui/table";
import { AnimatePresence, motion } from "framer-motion";
import { ArrowDownCircle, ArrowUpCircle, BadgeDollarSign, Calendar, CheckCircle, Clock, CreditCard, User, XCircle } from "lucide-react";
import { IBillDto } from "../../dtos/bills/find-all.dto";
import { getBankIcon } from "../../utils/get-bank-icon";
import { BillActionsDropdown } from "./bill-actions-dropdown";

export type AccountStatus = "paid" | "pending" | "overdue" | "canceled";

interface BillsTableProps {
	accounts: IBillDto[];
}

export function BillsTable({ accounts }: BillsTableProps) {
	console.log("accounts", accounts);
	const statusConfig = {
		paid: { classes: "bg-green-50 text-green-700", text: "Pago", icon: CheckCircle },
		pending: { classes: "bg-amber-50 text-amber-700", text: "Pendente", icon: Clock },
		overdue: { classes: "bg-red-50 text-red-700", text: "Vencido", icon: XCircle },
		canceled: { classes: "bg-gray-50 text-gray-700", text: "Cancelado", icon: XCircle },
		default: { classes: "bg-gray-50 text-gray-700", text: "Pendente", icon: Clock },
	};

	const getAccountStatus = (account: IBillDto): AccountStatus => {
		if (account.paymentDate) return "paid";

		if (account.dueDate) {
			const dueDate = new Date(account.dueDate.split(" ")[0].split("/").reverse().join("-"));
			const today = new Date();
			today.setHours(0, 0, 0, 0);

			if (dueDate < today) return "overdue";
		}

		return "pending";
	};

	const getTypeIcon = (type: string) => {
		if (type === "A Pagar") {
			return <ArrowDownCircle size={16} className="text-red-600 mr-1" />;
		}
		return <ArrowUpCircle size={16} className="text-green-600 mr-1" />;
	};

	const getValueColor = (type: string) => {
		return type === "A Pagar" ? "text-red-600" : "text-green-600";
	};

	return (
		<AnimatePresence mode="wait">
			<motion.div
				initial={{ opacity: 0, y: 20 }}
				animate={{ opacity: 1, y: 0 }}
				exit={{ opacity: 0, y: -20 }}
				transition={{ duration: 0.2 }}
				className="border rounded-[15px] border-gray-200 overflow-hidden"
			>
				<Table className="w-full rounded-[15px] text-sm">
					<TableHeader className="bg-gradient-to-r from-gray-50 to-white border-b border-gray-200">
						<TableRow>
							<TableHead className="text-center font-semibold">Descrição</TableHead>
							<TableHead className="text-center font-semibold">Tipo</TableHead>
							<TableHead className="text-center font-semibold">Valor</TableHead>
							<TableHead className="text-center font-semibold">Vencimento</TableHead>
							<TableHead className="text-center font-semibold">Pagamento</TableHead>
							<TableHead className="text-center font-semibold">Pessoa</TableHead>
							<TableHead className="text-center font-semibold">Conta</TableHead>
							<TableHead className="text-center font-semibold">Status</TableHead>
							<TableHead className="text-center font-semibold">Ações</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						{accounts.map(account => {
							const status = getAccountStatus(account);
							const config = statusConfig[status] ?? statusConfig.default;
							const StatusIcon = config.icon;

							return (
								<TableRow key={account.id} className="hover:bg-gray-50 transition-colors">
									<TableCell className="text-center font-medium">{account.description}</TableCell>
									<TableCell className="text-center">
										<div className="flex items-center justify-center">
											{getTypeIcon(account.type)}
											<span className={account.type === "A Pagar" ? "text-red-600" : "text-green-600"}>{account.type}</span>
										</div>
									</TableCell>
									<TableCell className={`text-center font-bold ${getValueColor(account.type)}`}>
										<div className="flex items-center justify-center">
											<BadgeDollarSign size={16} className={`${getValueColor(account.type)} mr-1`} />
											{account.value}
										</div>
									</TableCell>
									<TableCell className="text-center">
										{account.dueDate ? (
											<div className="flex items-center justify-center">
												<Calendar size={16} className="text-gray-500 mr-1" />
												{account.dueDate.split(" ")[0]}
											</div>
										) : (
											<span className="text-gray-400">-</span>
										)}
									</TableCell>
									<TableCell className="text-center">
										{account.paymentDate ? (
											<div className="flex items-center justify-center">
												<Calendar size={16} className="text-green-500 mr-1" />
												{account.paymentDate.split(" ")[0]}
											</div>
										) : (
											<span className="text-gray-400">-</span>
										)}
									</TableCell>
									<TableCell className="text-center">
										<div className="flex items-center justify-center">
											<User size={16} className="text-gray-500 mr-1" />
											<span className="truncate max-w-[150px]" title={account.person}>
												{account.person}
											</span>
										</div>
									</TableCell>
									<TableCell className="text-center">
										{account.account ? (
											<div className="flex items-center justify-center">
												{getBankIcon(account.account)}
												{account.account}
											</div>
										) : (
											<div className="flex items-center justify-center">
												<CreditCard size={16} className="text-gray-400 mr-1" />
												<span className="text-gray-400">-</span>
											</div>
										)}
									</TableCell>
									<TableCell className="text-center">
										<span className={`inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-medium ${config.classes}`}>
											<StatusIcon size={12} className="mr-1" />
											{config.text}
										</span>
									</TableCell>
									<TableCell className="text-center">
										<div className="flex items-center justify-center">
											<BillActionsDropdown bill={account} />
										</div>
									</TableCell>
								</TableRow>
							);
						})}
					</TableBody>
				</Table>
			</motion.div>
		</AnimatePresence>
	);
}
