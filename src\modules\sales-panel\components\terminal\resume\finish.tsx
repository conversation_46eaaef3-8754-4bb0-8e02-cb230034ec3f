import { useFinishOrderAndCoupon } from "@/modules/sales-panel/hooks/order/finish-mutation.hook";
import { orderIdAtom } from "@/modules/sales-panel/states/order-id.state";
import { OverlayContainer } from "@/shared/components/container/overlay-container";
import { Button } from "@/shared/components/ui/button";
import { useKeyboardShortcut } from "@/shared/hooks/use-keyboard-shortcut";
import { useModal } from "@/shared/hooks/utils/modal.hook";
import { useAtomValue } from "jotai";
import { Check, X } from "lucide-react";
import { toast } from "sonner";

export const FinishButton = () => {
	const orderId = useAtomValue(orderIdAtom);
	const { finishOrderAndShowCoupon, isLoading } = useFinishOrderAndCoupon();
	const { isOpen, toggleModal, closeModal } = useModal();

	useKeyboardShortcut({
		combination: { key: "f" },
		handler: () => {
			console.log("f");
			if (orderId) {
				toggleModal();
			}
		},
		options: {
			ignoreInputs: true,
			preventDefault: true,
			disabled: !orderId,
			fallback: () => {
				if (!orderId) {
					toast.error("Não há pedido em aberto para finalizar");
				}
			},
		},
	});

	return (
		<>
			<Button className="bg-mainColor shadow-main gap-2 flex text-white px-3 py-1 rounded-[15px]" onClick={toggleModal}>
				<Check size={18} />
				{isLoading ? "Finalizando..." : "Finalizar"}
				<span className="text-xs bg-white text-mainColor px-1 rounded">J</span>
			</Button>

			<OverlayContainer isVisible={isOpen} onClose={closeModal}>
				<div className="bg-white p-6 rounded-[15px] shadow-main max-w-md w-full">
					<div className="flex flex-col gap-4">
						<div className="flex justify-between items-center">
							<h2 className="text-xl font-semibold">Finalizar Pedido</h2>
							<button onClick={closeModal} className="text-gray-500 hover:text-gray-700">
								<X size={20} />
							</button>
						</div>

						<p className="text-gray-600">Você realmente deseja finalizar este pedido? Esta ação não pode ser desfeita.</p>

						<div className="flex justify-end gap-3 mt-4">
							<Button variant="outline" className="rounded-[15px]" onClick={closeModal}>
								Cancelar
							</Button>
							<Button
								className="bg-mainColor rounded-[15px] text-white"
								onClick={() => {
									if (orderId !== null) {
										finishOrderAndShowCoupon({ orderId });
										closeModal();
									}
								}}
							>
								Confirmar
							</Button>
						</div>
					</div>
				</div>
			</OverlayContainer>
		</>
	);
};
