import { useFindAccounts } from "@/modules/financial/hooks/accounts/find-all.hook";
import { getBankIcon } from "@/modules/financial/utils/get-bank-icon";
import { PersonsSelect } from "@/modules/person/components/person-select";
import { DatePickerInput } from "@/shared/components/custom/calendar-input";
import { Button } from "@/shared/components/ui/button";
import { Input } from "@/shared/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/shared/components/ui/select";
import { Calendar, CreditCard, Receipt, RefreshCw, Search } from "lucide-react";
import { Controller, FormProvider, UseFormReturn } from "react-hook-form";

interface FilterFormValues {
	description?: string;
	type?: string;
	dueDate?: Date | null;
	paymentDate?: Date | null;
	personId?: string;
	accountId?: string;
}

interface FilterFormProps {
	readonly methods: UseFormReturn<FilterFormValues>;
}

export function FilterForm({ methods }: FilterFormProps) {
	const { accounts, isLoading } = useFindAccounts();

	return (
		<FormProvider {...methods}>
			<form className="flex flex-col gap-3 md:flex-row md:items-end justify-between w-full">
				<div className="flex-1 min-w-0 md:w-[35%]">
					<label htmlFor="description" className="block text-sm font-medium text-gray-600 mb-1">
						Descrição
					</label>
					<div className="relative">
						<Input
							id="description"
							type="text"
							placeholder="Buscar por descrição..."
							className="w-full h-[45px] rounded-[10px] pl-10"
							{...methods.register("description")}
						/>
						<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>
				<div className="flex-1 min-w-0 md:w-[25%]">
					<label htmlFor="type" className="block text-sm font-medium text-gray-600 mb-1">
						Tipo
					</label>
					<div className="relative">
						<Select {...methods.register("type")}>
							<SelectTrigger id="type" className="w-full h-[45px] rounded-[10px] pl-10">
								<SelectValue placeholder="Todos os tipos" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="boleto">Boleto</SelectItem>
								<SelectItem value="fatura">Fatura</SelectItem>
							</SelectContent>
						</Select>
						<Receipt className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>
				<div className="flex-1 min-w-0 md:w-[25%]">
					<div className="relative">
						<Controller
							control={methods.control}
							name="dueDate"
							render={({ field }) => (
								<DatePickerInput
									className="w-full"
									field={field}
									inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
									placeholder="Data de vencimento"
									label="Data de Vencimento"
								/>
							)}
						/>
						<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>
				<div className="flex-1 min-w-0 md:w-[25%]">
					<div className="relative">
						<Controller
							control={methods.control}
							name="paymentDate"
							render={({ field }) => (
								<DatePickerInput
									className="w-full"
									field={field}
									inputDateClassName="w-full h-[45px] pl-10 rounded-[10px] border-gray-300"
									placeholder="Data de pagamento"
									label="Data de Pagamento"
								/>
							)}
						/>
						<Calendar className="absolute left-3 top-[47px] -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>
				<div className="flex-1 min-w-0 md:w-[25%]">
					<label htmlFor="personId" className="block text-sm font-medium text-gray-600 mb-1">
						Pessoa
					</label>
					<div className="relative">
						{/* <Select {...methods.register("personId")}>
							<SelectTrigger id="personId" className="w-full h-[45px] rounded-[10px] pl-10">
								<SelectValue placeholder="Todas as pessoas" />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value="1">Imobiliária Center</SelectItem>
								<SelectItem value="2">Companhia Elétrica</SelectItem>
								<SelectItem value="3">Telecom SA</SelectItem>
							</SelectContent>
						</Select>
						<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" /> */}
						<Controller
							control={methods.control}
							name="personId"
							render={({ field }) => <PersonsSelect value={field.value} onChange={field.onChange} />}
						/>
					</div>
				</div>
				<div className="flex-1 min-w-0 md:w-[25%]">
					<label htmlFor="accountId" className="block text-sm font-medium text-gray-600 mb-1">
						Conta
					</label>
					<div className="relative">
						<Controller
							control={methods.control}
							name="accountId"
							render={({ field }) => (
								<Select value={field.value ?? undefined} onValueChange={field.onChange} disabled={isLoading || !accounts}>
									<SelectTrigger id="accountId" className="w-full h-[45px] rounded-[10px] pl-10">
										<SelectValue placeholder={isLoading ? "Carregando contas..." : "Todas as contas"} />
									</SelectTrigger>
									<SelectContent>
										{accounts?.map(account => (
											<SelectItem key={account.id} value={String(account.id)}>
												<span className="flex items-center gap-2">
													{getBankIcon(account.name)}
													{account.name}
												</span>
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							)}
						/>
						<CreditCard className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-gray-400 pointer-events-none" />
					</div>
				</div>
				<div className="md:w-auto">
					<Button
						type="button"
						variant="outline"
						className="w-full md:w-auto h-[45px] rounded-[10px] flex items-center gap-2 whitespace-nowrap"
						onClick={() => methods.reset()}
					>
						<RefreshCw size={18} className="text-mainColor" />
						<span>Resetar Filtros</span>
					</Button>
				</div>
			</form>
		</FormProvider>
	);
}
